/**
 * Test Runs Management Module
 * Handles the Test Runs tab functionality including loading, displaying, and managing test execution history
 */

class TestRunsManager {
    constructor() {
        this.testRunsTable = document.getElementById('testRunsTable');
        this.testRunsTableBody = document.getElementById('testRunsTableBody');
        this.refreshBtn = document.getElementById('refreshTestRunsBtn');
        this.detailsModal = new bootstrap.Modal(document.getElementById('testRunDetailsModal'));
        this.detailsContent = document.getElementById('testRunDetailsContent');
        this.rerunWholeTestBtn = document.getElementById('rerunWholeTestBtn');
        
        this.currentExecutionId = null;
        
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Refresh button
        if (this.refreshBtn) {
            this.refreshBtn.addEventListener('click', () => this.loadTestRuns());
        }

        // Tab activation listener
        const testRunsTabBtn = document.getElementById('test-runs-tab-btn');
        if (testRunsTabBtn) {
            testRunsTabBtn.addEventListener('click', () => this.loadTestRuns());
        }

        // Rerun whole test button
        if (this.rerunWholeTestBtn) {
            this.rerunWholeTestBtn.addEventListener('click', () => this.rerunWholeTest());
        }
    }

    async loadTestRuns() {
        try {
            console.log('Loading test runs...');
            const response = await fetch('/api/test-runs');
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            
            if (data.status === 'success') {
                this.displayTestRuns(data.test_runs);
            } else {
                console.error('Error loading test runs:', data.message);
                this.showError('Failed to load test runs: ' + data.message);
            }
        } catch (error) {
            console.error('Error loading test runs:', error);
            this.showError('Failed to load test runs: ' + error.message);
        }
    }

    displayTestRuns(testRuns) {
        if (!this.testRunsTableBody) {
            console.error('Test runs table body not found');
            return;
        }

        // Clear existing content
        this.testRunsTableBody.innerHTML = '';

        if (!testRuns || testRuns.length === 0) {
            this.testRunsTableBody.innerHTML = `
                <tr>
                    <td colspan="4" class="text-center text-muted">
                        <i class="bi bi-inbox"></i> No test executions found
                    </td>
                </tr>
            `;
            return;
        }

        // Populate table with test runs
        testRuns.forEach(testRun => {
            const row = this.createTestRunRow(testRun);
            this.testRunsTableBody.appendChild(row);
        });
    }

    createTestRunRow(testRun) {
        const row = document.createElement('tr');
        row.className = 'test-run-row';
        row.style.cursor = 'pointer';

        // Format timestamp
        const timestamp = new Date(testRun.start_time).toLocaleString();
        
        // Determine status badge
        const statusBadge = this.getStatusBadge(testRun.status);
        
        // Create retry indicator
        const retryIndicator = testRun.has_retries ? 
            '<i class="bi bi-arrow-repeat text-warning ms-1" title="Has retries"></i>' : '';

        row.innerHTML = `
            <td>${timestamp}</td>
            <td>
                <code>${testRun.execution_id}</code>
                ${retryIndicator}
            </td>
            <td>${statusBadge}</td>
            <td>
                <button class="btn btn-sm btn-outline-primary me-1" onclick="testRunsManager.viewDetails('${testRun.execution_id}')">
                    <i class="bi bi-eye"></i> View
                </button>
                <button class="btn btn-sm btn-outline-secondary me-1" onclick="testRunsManager.reloadExecution('${testRun.execution_id}')">
                    <i class="bi bi-arrow-clockwise"></i> Reload
                </button>
                <button class="btn btn-sm btn-outline-danger" onclick="testRunsManager.deleteExecution('${testRun.execution_id}')">
                    <i class="bi bi-trash"></i> Delete
                </button>
            </td>
        `;

        // Add click listener for row
        row.addEventListener('click', (e) => {
            // Don't trigger if clicking on buttons
            if (!e.target.closest('button')) {
                this.viewDetails(testRun.execution_id);
            }
        });

        return row;
    }

    getStatusBadge(status) {
        switch (status) {
            case 'pass':
                return '<span class="badge bg-success">Passed</span>';
            case 'fail':
                return '<span class="badge bg-danger">Failed</span>';
            case 'running':
                return '<span class="badge bg-primary">Running</span>';
            default:
                return '<span class="badge bg-secondary">Unknown</span>';
        }
    }

    async viewDetails(executionId) {
        try {
            console.log('Loading details for execution:', executionId);
            this.currentExecutionId = executionId;
            
            const response = await fetch(`/api/test-runs/${executionId}`);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            
            if (data.status === 'success') {
                this.displayExecutionDetails(data.details);
                this.detailsModal.show();
            } else {
                console.error('Error loading execution details:', data.message);
                this.showError('Failed to load execution details: ' + data.message);
            }
        } catch (error) {
            console.error('Error loading execution details:', error);
            this.showError('Failed to load execution details: ' + error.message);
        }
    }

    displayExecutionDetails(details) {
        if (!this.detailsContent) {
            console.error('Details content container not found');
            return;
        }

        let html = `
            <div class="execution-details">
                <h6>Execution ID: <code>${details.execution_id}</code></h6>
                <div class="row">
        `;

        // Display test cases
        details.test_cases.forEach((testCase, index) => {
            const statusBadge = this.getStatusBadge(testCase.status);
            const retryBadge = testCase.retry ? 
                '<span class="badge bg-warning ms-2">Retry</span>' : '';

            html += `
                <div class="col-12 mb-3">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">${testCase.name} ${statusBadge} ${retryBadge}</h6>
                            <button class="btn btn-sm btn-outline-primary" onclick="testRunsManager.rerunTestCase('${testCase.name}')">
                                <i class="bi bi-play"></i> Rerun Test Case
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Step</th>
                                            <th>Action ID</th>
                                            <th>Status</th>
                                            <th>Timestamp</th>
                                        </tr>
                                    </thead>
                                    <tbody>
            `;

            testCase.steps.forEach((step, stepIndex) => {
                const stepStatusBadge = step.step_status === 'pass' ? 
                    '<span class="badge bg-success">Pass</span>' : 
                    '<span class="badge bg-danger">Fail</span>';

                html += `
                    <tr>
                        <td>${stepIndex + 1}</td>
                        <td><code>${step.action_id}</code></td>
                        <td>${stepStatusBadge}</td>
                        <td>${new Date(step.timestamp).toLocaleString()}</td>
                    </tr>
                `;
            });

            html += `
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });

        html += `
                </div>
            </div>
        `;

        this.detailsContent.innerHTML = html;
    }

    async reloadExecution(executionId) {
        try {
            console.log('Reloading execution:', executionId);
            
            const response = await fetch(`/api/test-runs/${executionId}/reload`, {
                method: 'POST'
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            
            if (data.status === 'success') {
                this.showSuccess('Execution reloaded successfully');
                this.loadTestRuns(); // Refresh the list

                // Populate the action list if we have action list data
                if (data.action_list && Array.isArray(data.action_list)) {
                    this.populateActionList(data.action_list);

                    // Switch to Device Control tab to show the action list
                    const deviceTab = document.getElementById('device-tab');
                    if (deviceTab) {
                        deviceTab.click();
                    }
                }
            } else {
                console.error('Error reloading execution:', data.message);
                this.showError('Failed to reload execution: ' + data.message);
            }
        } catch (error) {
            console.error('Error reloading execution:', error);
            this.showError('Failed to reload execution: ' + error.message);
        }
    }

    async deleteExecution(executionId) {
        if (!confirm(`Are you sure you want to delete execution ${executionId}?`)) {
            return;
        }

        try {
            console.log('Deleting execution:', executionId);
            
            const response = await fetch(`/api/test-runs/${executionId}`, {
                method: 'DELETE'
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            
            if (data.status === 'success') {
                this.showSuccess('Execution deleted successfully');
                this.loadTestRuns(); // Refresh the list
            } else {
                console.error('Error deleting execution:', data.message);
                this.showError('Failed to delete execution: ' + data.message);
            }
        } catch (error) {
            console.error('Error deleting execution:', error);
            this.showError('Failed to delete execution: ' + error.message);
        }
    }

    async rerunWholeTest() {
        if (!this.currentExecutionId) {
            this.showError('No execution selected');
            return;
        }

        try {
            console.log('Rerunning whole test for execution:', this.currentExecutionId);
            
            const response = await fetch(`/api/test-runs/${this.currentExecutionId}/rerun`, {
                method: 'POST'
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            
            if (data.status === 'success') {
                this.showSuccess('Test suite rerun started');
                this.detailsModal.hide();
                this.loadTestRuns(); // Refresh the list
            } else {
                console.error('Error rerunning test suite:', data.message);
                this.showError('Failed to rerun test suite: ' + data.message);
            }
        } catch (error) {
            console.error('Error rerunning test suite:', error);
            this.showError('Failed to rerun test suite: ' + error.message);
        }
    }

    async rerunTestCase(testCaseName) {
        try {
            console.log('Rerunning test case:', testCaseName);
            
            const response = await fetch(`/api/test-runs/rerun-test-case`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    test_case_name: testCaseName,
                    execution_id: this.currentExecutionId
                })
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            
            if (data.status === 'success') {
                this.showSuccess('Test case rerun started');
                this.detailsModal.hide();
                this.loadTestRuns(); // Refresh the list
            } else {
                console.error('Error rerunning test case:', data.message);
                this.showError('Failed to rerun test case: ' + data.message);
            }
        } catch (error) {
            console.error('Error rerunning test case:', error);
            this.showError('Failed to rerun test case: ' + error.message);
        }
    }

    populateActionList(actionList) {
        try {
            console.log('Populating action list with data:', actionList);

            // Get the action list element
            const actionsList = document.getElementById('actionsList');
            if (!actionsList) {
                console.error('Action list element not found');
                return;
            }

            // Clear existing content
            actionsList.innerHTML = '';

            // Populate with test execution data
            actionList.forEach((item, index) => {
                if (item.type === 'test_case_header') {
                    // Create test case header
                    const headerElement = document.createElement('div');
                    headerElement.className = `list-group-item test-case-header ${item.status === 'pass' ? 'success' : 'error'}`;
                    headerElement.innerHTML = `
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>Test Case: ${item.name}</strong>
                                ${item.retry ? '<span class="badge bg-warning ms-2">Retry</span>' : ''}
                            </div>
                            <span class="badge ${item.status === 'pass' ? 'bg-success' : 'bg-danger'}">${item.status}</span>
                        </div>
                    `;
                    actionsList.appendChild(headerElement);
                } else if (item.type === 'action_step') {
                    // Create action step
                    const stepElement = document.createElement('div');
                    stepElement.className = `list-group-item action-item ${item.status === 'pass' ? 'success' : 'error'}`;
                    stepElement.innerHTML = `
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <span class="step-number badge bg-secondary me-2">${item.step_idx + 1}</span>
                                <span>Action ID: ${item.action_id}</span>
                                ${item.multistep !== 'no' ? `<span class="badge bg-info ms-2">${item.multistep}</span>` : ''}
                            </div>
                            <div>
                                <span class="badge ${item.status === 'pass' ? 'bg-success' : 'bg-danger'} me-2">${item.status}</span>
                                <small class="text-muted">${new Date(item.timestamp).toLocaleTimeString()}</small>
                            </div>
                        </div>
                    `;
                    actionsList.appendChild(stepElement);
                }
            });

            console.log(`Populated action list with ${actionList.length} items`);
        } catch (error) {
            console.error('Error populating action list:', error);
        }
    }

    showSuccess(message) {
        // You can implement a toast notification system here
        console.log('Success:', message);
        alert(message); // Temporary implementation
    }

    showError(message) {
        // You can implement a toast notification system here
        console.error('Error:', message);
        alert(message); // Temporary implementation
    }
}

// Initialize the Test Runs Manager when the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.testRunsManager = new TestRunsManager();
    console.log('Test Runs Manager initialized');
});
